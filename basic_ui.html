<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Gateway WebSocket Test Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.error { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .log {
            background: #000;
            color: #00ff00;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            border-radius: 4px;
        }
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }
    </style>
</head>
<body>
    <h1>Voice Gateway WebSocket Test Client</h1>
    
    <div class="container">
        <h3>Connection Settings</h3>
        <div>
            <label>WebSocket URL:</label>
            <input type="text" id="wsUrl" value="ws://localhost:8002" style="width: 200px;">
        </div>
        <div>
            <label>JWT Token:</label>
            <input type="text" id="jwtToken" placeholder="Enter JWT token or leave empty for test token" style="width: 400px;">
        </div>
        <div>
            <label>Call ID:</label>
            <input type="text" id="callId" style="width: 200px;">
        </div>
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
        </div>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
    </div>

    <div class="container">
        <h3>Control Messages</h3>
        <div>
            <button onclick="sendPing()" disabled id="pingBtn">Send Ping</button>
            <button onclick="sendConfigUpdate()" disabled id="configBtn">Update Config</button>
            <button onclick="endCall()" disabled id="endCallBtn">End Call</button>
        </div>
        <div>
            <label>Target Language:</label>
            <select id="targetLang">
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
                <option value="it">Italian</option>
            </select>
        </div>
    </div>

    <div class="container">
        <h3>Audio Testing</h3>
        <div class="audio-controls">
            <button onclick="startRecording()" disabled id="recordBtn">Start Recording</button>
            <button onclick="stopRecording()" disabled id="stopBtn">Stop Recording</button>
            <button onclick="sendTestAudio()" disabled id="testAudioBtn">Send Test Audio</button>
            <input type="file" id="audioFile" accept="audio/*">
            <button onclick="sendAudioFile()" disabled id="fileBtn">Send Audio File</button>
        </div>
        <div>
            <audio id="audioPlayback" controls style="width: 100%; margin-top: 10px;"></audio>
        </div>
    </div>

    <div class="container">
        <h3>Message Log</h3>
        <div id="messageLog" class="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        let websocket = null;
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        // Generate a random call ID
        document.getElementById('callId').value = 'test-call-' + Math.random().toString(36).substr(2, 9);

        function log(message, type = 'info') {
            console.log(message);
            const logDiv = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'sent' ? '#4ecdc4' : '#00ff00';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = message;
            
            // Update button states
            const isConnected = status === 'connected';
            document.getElementById('connectBtn').disabled = isConnected;
            document.getElementById('disconnectBtn').disabled = !isConnected;
            document.getElementById('pingBtn').disabled = !isConnected;
            document.getElementById('configBtn').disabled = !isConnected;
            document.getElementById('endCallBtn').disabled = !isConnected;
            document.getElementById('recordBtn').disabled = !isConnected;
            document.getElementById('testAudioBtn').disabled = !isConnected;
            document.getElementById('fileBtn').disabled = !isConnected;
        }

        async function generateTestToken() {
            // For browser testing, we need to get a real token from the auth service
            // or use a test token for development mode

            try {
                // Try to get a real token from the auth service
                const authUrl = 'http://localhost:8001/api/v1/auth/login';
                const response = await fetch(authUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'AdminPassword123!'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    log('Successfully obtained real JWT token from auth service', 'info');
                    return data.data.access_token;
                }
            } catch (error) {
                log(`Auth service not available: ${error.message}`, 'info');
            }

            // Fallback: Use test token
            log('Using test token', 'info');
            return 'test-token';
        }

        async function connect() {
            const wsUrl = document.getElementById('wsUrl').value;
            const callId = document.getElementById('callId').value;
            let token = document.getElementById('jwtToken').value;

            if (!token) {
                log('No token provided, attempting to get one from auth service...', 'info');
                token = await generateTestToken();
                if (!token) {
                    log('Failed to obtain JWT token. Please enter a valid token manually.', 'error');
                    return;
                }
                log('Successfully obtained JWT token', 'info');
            }

            const fullUrl = `${wsUrl}/api/v1/call/ws/call/${callId}?token=${token}`;
            
            try {
                log(`Connecting to: ${fullUrl}`, 'info');
                websocket = new WebSocket(fullUrl);
                
                websocket.onopen = function(event) {
                    log('WebSocket connection established', 'info');
                    updateConnectionStatus('connected', 'Connected');
                };
                
                websocket.onmessage = function(event) {
                    if (event.data instanceof Blob) {
                        log(`Received translated audio: ${event.data.size} bytes`, 'info');
                        
                        // Create audio URL and play it
                        const audioUrl = URL.createObjectURL(event.data);
                        const audioElement = document.getElementById('audioPlayback');
                        audioElement.src = audioUrl;
                        audioElement.play().catch(e => log(`Audio playback error: ${e.message}`, 'error'));
                        
                    } else {
                        try {
                            const message = JSON.parse(event.data);
                            log(`Received: ${JSON.stringify(message, null, 2)}`, 'info');
                            
                            if (message.type === 'connection_established') {
                                log('Connection confirmed by server', 'info');
                            } else if (message.type === 'pong') {
                                log('Received pong response', 'info');
                            } else if (message.type === 'error') {
                                log(`Server error: ${message.message}`, 'error');
                            }
                        } catch (e) {
                            log(`Received non-JSON message: ${event.data}`, 'info');
                        }
                    }
                };
                
                websocket.onerror = function(error) {
                    log(`WebSocket error: ${error}`, 'error');
                    updateConnectionStatus('error', 'Connection Error');
                };
                
                websocket.onclose = function(event) {
                    log(`WebSocket closed: ${event.code} - ${event.reason}`, 'info');
                    updateConnectionStatus('disconnected', 'Disconnected');
                    websocket = null;
                };
                
            } catch (error) {
                log(`Connection failed: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Connection Failed');
            }
        }

        function disconnect() {
            if (websocket) {
                websocket.close();
                log('Disconnecting...', 'info');
            }
        }

        function sendPing() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {type: "ping"};
                websocket.send(JSON.stringify(message));
                log(`Sent: ${JSON.stringify(message)}`, 'sent');
            }
        }

        function sendConfigUpdate() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const targetLang = document.getElementById('targetLang').value;
                const message = {
                    type: "config_update",
                    config: {
                        target_language: targetLang,
                        voice_speed: 1.0
                    }
                };
                websocket.send(JSON.stringify(message));
                log(`Sent: ${JSON.stringify(message)}`, 'sent');
            }
        }

        function endCall() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = {type: "end_call"};
                websocket.send(JSON.stringify(message));
                log(`Sent: ${JSON.stringify(message)}`, 'sent');
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        sampleRate: 16000,
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true
                    } 
                });
                
                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];
                
                mediaRecorder.ondataavailable = function(event) {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = function() {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    sendAudioBlob(audioBlob);
                };
                
                mediaRecorder.start();
                isRecording = true;
                document.getElementById('recordBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                log('Started recording...', 'info');
                
            } catch (error) {
                log(`Recording error: ${error.message}`, 'error');
            }
        }

        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                mediaRecorder.stream.getTracks().forEach(track => track.stop());
                isRecording = false;
                document.getElementById('recordBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;
                log('Stopped recording', 'info');
            }
        }

        function sendTestAudio() {
            // Generate synthetic audio data
            const sampleRate = 16000;
            const duration = 2; // seconds
            const samples = sampleRate * duration;
            const audioData = new Int16Array(samples);
            
            // Generate a simple sine wave
            for (let i = 0; i < samples; i++) {
                const t = i / sampleRate;
                audioData[i] = Math.sin(2 * Math.PI * 440 * t) * 16000; // 440Hz tone
            }
            
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(audioData.buffer);
                log(`Sent synthetic audio: ${audioData.buffer.byteLength} bytes`, 'sent');
            }
        }

        async function sendAudioFile() {
            const fileInput = document.getElementById('audioFile');
            const file = fileInput.files[0];
            
            if (!file) {
                log('Please select an audio file first', 'error');
                return;
            }
            
            sendAudioBlob(file);
        }

        async function sendAudioBlob(blob) {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const arrayBuffer = await blob.arrayBuffer();
                websocket.send(arrayBuffer);
                log(`Sent audio file: ${arrayBuffer.byteLength} bytes`, 'sent');
            }
        }

        function clearLog() {
            document.getElementById('messageLog').innerHTML = '';
        }

        // Initialize
        updateConnectionStatus('disconnected', 'Disconnected');
    </script>
</body>
</html>
